package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.ApplicationUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.repository.jpa.JuziSingleChatContentRecordJpaRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiHumanNotJoinComp;
import com.bj58.spat.esbclient.*;
import com.bj58.spat.esbclient.config.DelayLevel;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class LbgAiAsyncReplyHandler {

    @Autowired
    protected WmbProperties wmbProperties;

    @Autowired
    private LbgAiReplyComponent lbgAiReplyComponent;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 10,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("lbg_ai_async_reply-%d").build(),
            new BlockPolicy(Runnable::run)
    );


    @PostConstruct
    protected void init() {
        try {
            Method initMethod = wmbHandler.getClass().getDeclaredMethod("init");
            initMethod.setAccessible(true);
            initMethod.invoke(wmbHandler);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 故意的没有构建成Spring的Bean
     */
    private final AbstractWmbHandler wmbHandler = new AbstractWmbHandler() {

        private ESBClient client;

        private WmbProperties.Client.Publish publish;

        @Override
        protected int getWmbSubjectId() {
            return publish.getSubjectId();
        }

        @Override
        protected ESBClient getWmbClient() {
            return this.client;
        }

        @Override
        protected String getWarningPrefix() {
            return "[lbg_ai_chat]";
        }

        @Override
        public void init() {
            WmbProperties properties = LbgAiAsyncReplyHandler.this.wmbProperties;
            publish = properties.getPublish("lbg_ai_chat");
            WmbProperties.Client.Subscribe subscribe = properties.getSubscribe("lbg_ai_chat");

            if (Objects.isNull(publish) && Objects.isNull(subscribe)) {
                log.info("No configuration item exists, {} wmb client init failure. ", getWarningPrefix());
                return;
            }

            try {
                client = new ESBClient(properties.getPath());
                log.info("{} esb client init success", getWarningPrefix());

                if (!subscribe.isEnabled()) {
                    log.info("{} esb subscribe function has not been activated", getWarningPrefix());
                    return;
                }

                client.setReceiveSubject(new ESBSubject(
                        subscribe.getSubjectId(), subscribe.getClientId(), SubMode.PULL
                ));
                client.setReceiveQmaxLen(32);
                client.setReceiveHandler(new ESBReceiveHandler() {
                    @Override
                    public void messageReceived(final ESBMessage esbMessage) {
                        handlerMessage(esbMessage);
                    }
                });
                client.startAsyncPull();
                log.info("{} esb receive handler init success", getWarningPrefix());
            } catch (Exception e) {
                log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
            }
        }
    };

    public void commitAsyncAiReplyTask(@NonNull final String corpId,
                                       @NonNull final String botUserId,
                                       @NonNull final String externalUserId,
                                       @NonNull final String contactName) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", botUserId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("contactName", contactName);
        bodyInfoObj.put("type", "async_customer_ai_chat");

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), DelayLevel.DELAY_30S);

        } else {
            new Thread(() -> { // 沙箱MOCK数据，30S后执行
                try {
                    TimeUnit.SECONDS.sleep(30);
                } catch (InterruptedException ignored) {
                }
                // 沙箱直接执行
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    // public void commitAsyncAskChatFinish(@NonNull final String corpId,
    //                                             @NonNull final String botUserId,
    //                                             @NonNull final String externalUserId) {
    //
    //     Map<String, Object> bodyInfoObj = new HashMap<>();
    //     bodyInfoObj.put("corpId", corpId);
    //     bodyInfoObj.put("botUserId", botUserId);
    //     bodyInfoObj.put("externalUserId", externalUserId);
    //     bodyInfoObj.put("type", "async_customer_ai_chat_finish");
    //
    //     wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), DelayLevel.DELAY_5M);
    // }

    public void commitDelayCheckHumanNotJoinThenSendTip2ExternalUser(final @NonNull String corpId,
                                                                     final @NonNull String userId,
                                                                     final @NonNull String externalUserId) {
        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", userId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("type", "human_not_join_then_send_tip_2_external_user");

        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 4 * 60);
    }

    public void commitDelayCheckHumanNotJoinThenForceTipUser(final @NonNull String corpId,
                                                             final @NonNull String userId,
                                                             final @NonNull String externalUserId,
                                                             final @NonNull String contactName) {

        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", userId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("contactName", contactName);
        bodyInfoObj.put("type", "human_not_join_then_force_tip_user");

        wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 3 * 60);
    }

    /**
     * 提交延迟检查任务
     */
    public void commitDelayCheckFeedbackTask(@NonNull final String corpId,
                                             @NonNull final String userId,
                                             @NonNull final String externalUserId,
                                             @NonNull final JuziSingleChatContentRecord singleChatContentRecord) {
        Date eventTimestamp = singleChatContentRecord.getCreateTime();

        // 是否上班时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(eventTimestamp);
        final int curHour = calendar.get(Calendar.HOUR_OF_DAY);

        // 09:00 - 20:00
        if (curHour >= 9 && curHour <= 19) {

            Map<String, Object> bodyInfoObj = new HashMap<>();
            bodyInfoObj.put("corpId", corpId);
            bodyInfoObj.put("botUserId", userId);
            bodyInfoObj.put("externalUserId", externalUserId);
            bodyInfoObj.put("timestamp", System.currentTimeMillis());
            bodyInfoObj.put("type", "check_feedback_recovery");

            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 30 * 60);
        }
    }

    /**
     * 提交延迟创建客诉单任务
     * 3分钟后检查用户是否有回复，如果没有回复则创建客诉单
     */
    public void commitDelayedComplaintCreationTask(@NonNull final String corpId,
                                                   @NonNull final String botUserId,
                                                   @NonNull final String externalUserId,
                                                   @NonNull final String contactName,
                                                   @NonNull final String complaintParams) {
        Map<String, Object> bodyInfoObj = new HashMap<>();
        bodyInfoObj.put("corpId", corpId);
        bodyInfoObj.put("botUserId", botUserId);
        bodyInfoObj.put("externalUserId", externalUserId);
        bodyInfoObj.put("contactName", contactName);
        bodyInfoObj.put("complaintParams", complaintParams);
        bodyInfoObj.put("type", "delayed_complaint_creation");

        // 3分钟延迟
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            wmbHandler.doSend(JSON.toJSONString(bodyInfoObj), 3 * 60);
        } else {
            // 沙箱环境直接执行（用于测试）
            new Thread(() -> {
                try {
                    TimeUnit.SECONDS.sleep(3 * 60);
                } catch (InterruptedException ignored) {
                }
                ESBMessage message = new ESBMessage();
                message.setBody(JSON.toJSONString(bodyInfoObj).getBytes(StandardCharsets.UTF_8));
                handlerMessage(message);
            }).start();
        }
    }

    private void handlerMessage(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        Runnable runnable = () -> {
            log.info("receive lbg ai async ai chat job, body = {}", esbMsg);

            JSONObject esbMsgObj = JSON.parseObject(esbMsg);
            String corpId = esbMsgObj.getString("corpId");
            String botUserId = esbMsgObj.getString("botUserId");
            String externalUserId = esbMsgObj.getString("externalUserId");

            String type = esbMsgObj.getString("type");

            if (StringUtils.isBlank(botUserId) || StringUtils.isBlank(externalUserId) || StringUtils.isBlank(type)) {
                return;
            }

            LbgAiReplyComponent lbgAiReplyComponent =
                    ApplicationUtils.getBean(LbgAiReplyComponent.class);

            LbgAiHumanNotJoinComp humanNotJoinComp =
                    ApplicationUtils.getBean(LbgAiHumanNotJoinComp.class);

            try {
                if (StringUtils.equalsIgnoreCase(type, "async_customer_ai_chat")) {
                    String contactName = esbMsgObj.getString("contactName");
                    lbgAiReplyComponent.tryReply(corpId, botUserId, externalUserId, contactName);

                } else if (StringUtils.equalsIgnoreCase(type, "human_not_join_then_send_tip_2_external_user")) {

                    humanNotJoinComp.humanNotJoinThenSendTip2ExternalUser(corpId, botUserId, externalUserId);

                } else if (StringUtils.equalsIgnoreCase(type, "human_not_join_then_force_tip_user")) {
                    String contactName = esbMsgObj.getString("contactName");
                    humanNotJoinComp.humanNotJoinThenForceTipUser(corpId, botUserId, externalUserId, contactName);
                } else if (StringUtils.equalsIgnoreCase(type, "check_feedback_recovery")) {
                    Long timestamp = esbMsgObj.getLong("timestamp");
                    lbgAiReplyComponent.checkAndSendFeedbackRecovery(corpId, botUserId, externalUserId, timestamp);
                } else if (StringUtils.equalsIgnoreCase(type, "delayed_complaint_creation")) {
                    String contactName = esbMsgObj.getString("contactName");
                    String complaintParams = esbMsgObj.getString("complaintParams");
                    handleDelayedComplaintCreation(corpId, botUserId, externalUserId, contactName, complaintParams);
                }

                log.info("lbg ai chat job process completion, corpId = {}, botUserId = {}, externalUserId = {}",
                        corpId, botUserId, externalUserId);

            } catch (Exception e) {

                log.error("lbg ai chat job process completion, corpId = {}, botUserId = {}, externalUserId = {}, " +
                        "ex msg = {}", corpId, botUserId, externalUserId, e.getMessage(), e);
            }

        };

        executor.execute(runnable);
    }

    /**
     * 处理延迟创建客诉单
     * 检查3分钟内用户是否有回复，如果没有回复则创建客诉单
     */
    private void handleDelayedComplaintCreation(@NonNull final String corpId,
                                                @NonNull final String botUserId,
                                                @NonNull final String externalUserId,
                                                @NonNull final String contactName,
                                                @NonNull final String complaintParams) {
        try {
            log.info("开始处理延迟创建客诉单任务, corpId: {}, botUserId: {}, externalUserId: {}, complaintParams: {}",
                    corpId, botUserId, externalUserId, complaintParams);

            // 检查3分钟内是否有用户回复
            boolean hasUserReply = checkUserReplyInLastMinutes(corpId, botUserId, externalUserId, 3);

            if (hasUserReply) {
                log.info("检测到用户在3分钟内有回复，取消创建客诉单, corpId: {}, botUserId: {}, externalUserId: {}",
                        corpId, botUserId, externalUserId);
                return;
            }

            log.info("用户在3分钟内无回复，开始创建客诉单, corpId: {}, botUserId: {}, externalUserId: {}",
                    corpId, botUserId, externalUserId);

            // 构造extend参数
            Map<String, String> extend = new HashMap<>();
            extend.put("sceneState", "创建客诉单");
            extend.put("complaintParams", complaintParams);

            // 调用创建客诉单方法
            lbgAiReplyComponent.tryCreateComplaintOrder(extend, corpId, botUserId, externalUserId, contactName);

        } catch (Exception e) {
            log.error("处理延迟创建客诉单异常, corpId: {}, botUserId: {}, externalUserId: {}",
                    corpId, botUserId, externalUserId, e);
        }
    }

    /**
     * 检查指定分钟内是否有用户回复
     */
    private boolean checkUserReplyInLastMinutes(@NonNull final String corpId,
                                                @NonNull final String botUserId,
                                                @NonNull final String externalUserId,
                                                int minutes) {
        try {
            // 获取JPA Repository
            JuziSingleChatContentRecordJpaRepository jpaRepository =
                ApplicationUtils.getBean(JuziSingleChatContentRecordJpaRepository.class);

            // 计算时间范围
            Date endTime = new Date();
            Date startTime = new Date(endTime.getTime() - (minutes * 60 * 1000L));

            // 查询指定时间范围内的消息
            List<JuziSingleChatContentRecordEntity> messages = jpaRepository.findChatRecords(
                    corpId, botUserId, startTime, endTime);

            if (ObjectUtils.isEmpty(messages)) {
                return false;
            }

            // 检查是否有用户发送的消息（sendBy=1表示外部联系人发送）
            for (JuziSingleChatContentRecordEntity message : messages) {
                if (message.getSendBy() == 1 && externalUserId.equals(message.getExternalUserId())) {
                    log.info("检测到用户在{}分钟内有回复, messageId: {}, createTime: {}, corpId: {}, botUserId: {}, externalUserId: {}",
                            minutes, message.getMessageId(), message.getCreateTime(), corpId, botUserId, externalUserId);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户回复异常, corpId: {}, botUserId: {}, externalUserId: {}, minutes: {}",
                    corpId, botUserId, externalUserId, minutes, e);
            // 异常情况下，为了安全起见，认为用户有回复，不创建客诉单
            return true;
        }
    }
}
