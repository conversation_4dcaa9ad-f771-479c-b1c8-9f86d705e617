package com.bj58.hy.wx.qywxbiz.interfaces.web.sandbox;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.huangye.alg.qasystem.client.entity.AiCustomerDaojiaResult;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.repository.DifyChatAnalysisResultRepository;
import com.bj58.hy.wx.qywxbiz.repository.JuziSingleChatContentRecordDirectRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiReplyComponent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dify聊天分析测试Controller
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/test/dify-chat-analysis")
public class DifyChatAnalysisTestController {

    @Autowired
    private JuziSingleChatContentRecordDirectRepository juziSingleChatContentRecordDirectRepository;

    @Autowired
    private DifyChatAnalysisResultRepository difyChatAnalysisResultRepository;

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Autowired
    private LbgAiReplyComponent lbgAiReplyComponent;

    // Dify工作流API Key，与Job Handler保持一致
    private static final String DIFY_WORKFLOW_API_KEY = "app-MNKfY8RzFXnfnl767durVTcy";
    private static final String JINGXUAN_CORP_ID = "ww5cfa32107e9a1f20";

    /**
     * 测试单个用户的聊天分析
     */
    @PostMapping("/analyze-user-chat")
    public Result<?> analyzeUserChat(@Valid @RequestBody AnalyzeUserChatReq req) {
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return Result.failure("生产环境不支持此操作");
        }

        try {
            log.info("开始测试用户聊天分析，userId={}, analysisDate={}", req.getUserId(), req.getAnalysisDate());

            // 1. 获取聊天记录
            Map<String, List<LbgAiMessageChatRecordBo>> chatRecordsByExternalUser =
                    juziSingleChatContentRecordDirectRepository.getChatRecordsByExternalUser(JINGXUAN_CORP_ID, req.getUserId(), req.getAnalysisDate());

            if (ObjectUtils.isEmpty(chatRecordsByExternalUser)) {
                return Result.success("该用户在指定日期无聊天记录");
            }

            // 2. 构造响应数据
            AnalyzeUserChatResp response = new AnalyzeUserChatResp();
            response.setUserId(req.getUserId());
            response.setAnalysisDate(req.getAnalysisDate());
            response.setExternalUserCount(chatRecordsByExternalUser.size());

            // 手动调用Job Handler的处理逻辑
            int processedCount = processUserChatRecords(req.getUserId(), req.getAnalysisDate(), chatRecordsByExternalUser);
            response.setProcessedCount(processedCount);
            response.setMessage("分析完成");

            return Result.success(response);

        } catch (Exception e) {
            log.error("测试用户聊天分析失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }


    /**
     * 处理用户聊天记录（使用真实的Job Handler逻辑）
     */
    private int processUserChatRecords(String userId, String analysisDate,
                                       Map<String, List<LbgAiMessageChatRecordBo>> chatRecordsByExternalUser) {
        int processedCount = 0;

        for (Map.Entry<String, List<LbgAiMessageChatRecordBo>> entry : chatRecordsByExternalUser.entrySet()) {
            String externalUserId = entry.getKey();
            List<LbgAiMessageChatRecordBo> chatMessages = entry.getValue();

            try {
                // 检查是否已经分析过
                if (difyChatAnalysisResultRepository.existsByUserAndDate(userId, externalUserId, analysisDate)) {
                    log.info("客服 {} 与外部用户 {} 在 {} 的聊天记录已分析过，跳过", userId, externalUserId, analysisDate);
                    continue;
                }

                // 调用真实的Dify分析逻辑
                processSingleUserChatRecord(userId, externalUserId, analysisDate, chatMessages);
                processedCount++;

            } catch (Exception e) {
                log.error("处理客服 {} 与外部用户 {} 的聊天记录失败", userId, externalUserId, e);
                // 保存失败记录
                saveChatAnalysisFailure(userId, externalUserId, analysisDate, "处理异常：" + e.getMessage(), chatMessages.size());
            }
        }

        return processedCount;
    }

    /**
     * 处理单个用户的聊天记录（复用Job Handler逻辑）
     */
    private void processSingleUserChatRecord(String userId, String externalUserId,
                                             String analysisDate, List<LbgAiMessageChatRecordBo> chatMessages) {
        try {
            String difyResult = callDifyForChatAnalysis(userId, externalUserId, chatMessages);

            if (StringUtils.isNotEmpty(difyResult)) {
                // 提取category_4id字段
                String category4Id = extractCategory4Id(difyResult);

                difyChatAnalysisResultRepository.save(userId, externalUserId, analysisDate, difyResult, chatMessages.size(), category4Id);
                log.info("成功分析客服 {} 与外部用户 {} 的聊天记录，消息数量：{}，category4Id：{}",
                        userId, externalUserId, chatMessages.size(), category4Id);
            } else {
                saveChatAnalysisFailure(userId, externalUserId, analysisDate, "Dify分析返回空结果", chatMessages.size());
            }

        } catch (Exception e) {
            saveChatAnalysisFailure(userId, externalUserId, analysisDate, "处理异常：" + e.getMessage(), chatMessages.size());
            log.error("处理客服 {} 与外部用户 {} 的聊天记录失败", userId, externalUserId, e);
        }
    }

    /**
     * 调用Dify分析聊天记录（复用Job Handler逻辑）
     */
    private String callDifyForChatAnalysis(String userId, String externalUserId,
                                           List<LbgAiMessageChatRecordBo> chatMessages) {
        if (ObjectUtils.isEmpty(chatMessages)) {
            log.warn("聊天记录为空，跳过Dify分析，客服：{}，外部用户：{}", userId, externalUserId);
            return null;
        }

        String user = String.format("%s:%s:%s", JINGXUAN_CORP_ID, userId, externalUserId);
        log.info("调用Dify分析聊天记录，用户：{}，消息数量：{}", user, chatMessages.size());

        try {
            WorkflowRequest request = new WorkflowRequest();
            request.setUser(user);
            request.getInputs().put("chatList", JacksonUtils.format(chatMessages));

            Result<WorkflowResponse> workflow = difyRemoteService.workflow(DIFY_WORKFLOW_API_KEY, request);

            validateDifyResponse(workflow, user);

            String result = JacksonUtils.format(workflow.getData().getData());
            log.info("Dify分析成功，用户：{}，结果长度：{}", user, result.length());
            return result;

        } catch (Exception e) {
            log.error("调用Dify分析聊天记录失败，用户：{}", user, e);
            throw e;
        }
    }

    /**
     * 验证Dify响应（复用Job Handler逻辑）
     */
    private void validateDifyResponse(Result<WorkflowResponse> workflow, String user) {
        if (workflow == null) {
            throw new RuntimeException("Dify服务返回null");
        }
        if (workflow.isFailed()) {
            throw new RuntimeException("Dify服务调用失败：" + workflow.getMsg());
        }
        if (ObjectUtils.isEmpty(workflow.getData()) || ObjectUtils.isEmpty(workflow.getData().getData())) {
            log.warn("Dify分析返回空数据，用户：{}", user);
            throw new RuntimeException("Dify分析返回空数据");
        }
    }

    /**
     * 从Dify分析结果中提取category_4id字段（复用Job Handler逻辑）
     */
    private String extractCategory4Id(String difyResult) {
        try {
            if (StringUtils.isEmpty(difyResult)) {
                return null;
            }

            JSONObject jsonObject = JSON.parseObject(difyResult);
            if (jsonObject != null) {
                // 先尝试从outputs字段中提取
                JSONObject outputs = jsonObject.getJSONObject("outputs");
                if (outputs != null && outputs.containsKey("category_4id")) {
                    String category4Id = outputs.getString("category_4id");
                    return StringUtils.isNotEmpty(category4Id) && !"0".equals(category4Id) ? category4Id : null;
                }

                // 兼容：如果outputs中没有，尝试从根级别提取
                if (jsonObject.containsKey("category_4id")) {
                    String category4Id = jsonObject.getString("category_4id");
                    return StringUtils.isNotEmpty(category4Id) && !"0".equals(category4Id) ? category4Id : null;
                }
            }

            return null;

        } catch (Exception e) {
            log.warn("解析Dify结果提取category_4id失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 保存聊天分析失败记录（复用Job Handler逻辑）
     */
    private void saveChatAnalysisFailure(String userId, String externalUserId, String analysisDate,
                                         String failureReason, int messageCount) {
        try {
            difyChatAnalysisResultRepository.saveFailure(userId, externalUserId, analysisDate, failureReason, messageCount);
            log.warn("Dify分析失败，客服：{}，外部用户：{}，原因：{}", userId, externalUserId, failureReason);
        } catch (Exception e) {
            log.error("保存失败记录时发生异常", e);
        }
    }

    /**
     * 根据日期删除Dify分析结果数据
     */
    @PostMapping("/delete-by-date")
    public Result<?> deleteByDate(@Valid @RequestParam String analysisDate) {
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return Result.failure("生产环境不支持此操作");
        }

        try {
            long deletedCount;
            String operation;

            deletedCount = difyChatAnalysisResultRepository.deleteByAnalysisDate(analysisDate);
            operation = "按单个日期删除";

            DeleteByDateResp response = new DeleteByDateResp();
            response.setDeletedCount(deletedCount);
            response.setOperation(operation);
            response.setMessage("删除操作完成");

            log.info("删除Dify分析结果数据完成，操作类型：{}，删除记录数：{}", operation, deletedCount);
            return Result.success(response);

        } catch (Exception e) {
            log.error("删除Dify分析结果数据失败", e);
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    /**
     * 测试tryCreateComplaintOrder方法
     */
    @PostMapping("/test-create-complaint-order")
    public Result<?> testCreateComplaintOrder(@Valid @RequestBody TestCreateComplaintOrderReq req) {
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return Result.failure("生产环境不支持此操作");
        }

        try {
            log.info("开始测试创建客诉单，请求参数：{}", JacksonUtils.format(req));

            // 构造测试用的AiCustomerDaojiaResult
            AiCustomerDaojiaResult aiResult = new AiCustomerDaojiaResult();
            aiResult.setType(4); // 执行操作

            // 构造extend参数
            Map<String, String> extend = new HashMap<>();
            extend.put("sceneState", "创建客诉单");

            // 构造complainParams JSON
            JSONObject complainParams = new JSONObject();
            complainParams.put("orderId", req.getOrderId());
            complainParams.put("beComplainIdentity", req.getBeComplainIdentity());
            complainParams.put("complainDescription", req.getComplainDescription());
            complainParams.put("complainReason", req.getComplainReason());
            complainParams.put("complainType", req.getComplainType());
            complainParams.put("isChangePhone", req.getIsChangePhone());
            if (ObjectUtils.notEmpty(req.getComplainPhone())) {
                complainParams.put("complainPhone", req.getComplainPhone());
            }

            extend.put("complainParams", complainParams.toJSONString());
            aiResult.setExtend(extend);

            // 调用handleAsyncExecuteOperation方法测试tryCreateComplaintOrder逻辑
            String corpId = JINGXUAN_CORP_ID;
            String botUserId = "majingxian";
            String externalUserId = "wmOrVVCwAAebOGnTreOPX1MmNTUoDjmg";
            String contactName = req.getContactName() != null ? req.getContactName() : "测试用户";

            lbgAiReplyComponent.handleAsyncExecuteOperation(corpId, botUserId, externalUserId, contactName, aiResult);

            TestCreateComplaintOrderResp response = new TestCreateComplaintOrderResp();
            response.setOrderId(req.getOrderId());
            response.setTestParams(complainParams.toJSONString());
            response.setCorpId(corpId);
            response.setBotUserId(botUserId);
            response.setExternalUserId(externalUserId);
            response.setContactName(contactName);
            response.setMessage("测试执行完成，请查看日志了解详细执行结果");

            log.info("测试创建客诉单完成，订单ID：{}，参数：{}", req.getOrderId(), complainParams.toJSONString());
            return Result.success(response);

        } catch (Exception e) {
            log.error("测试创建客诉单失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试延迟创建客诉单功能
     */
    @PostMapping("/test-delayed-complaint-creation")
    public Result<?> testDelayedComplaintCreation(@Valid @RequestBody TestDelayedComplaintCreationReq req) {
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            return Result.failure("生产环境不支持此操作");
        }

        try {
            log.info("开始测试延迟创建客诉单功能，请求参数：{}", JacksonUtils.format(req));

            // 构造测试用的extend参数
            Map<String, String> extend = new HashMap<>();
            extend.put("isPushComplaint", "true");

            // 构造complainParams
            JSONObject complainParams = new JSONObject();
            complainParams.put("orderId", req.getOrderId());
            complainParams.put("beComplainIdentity", req.getBeComplainIdentity() != null ? req.getBeComplainIdentity() : "商家");
            complainParams.put("complainDescription", req.getComplainDescription() != null ? req.getComplainDescription() : "测试延迟创建客诉单");
            complainParams.put("isChangePhone", req.getIsChangePhone() != null ? req.getIsChangePhone() : false);
            if (req.getComplainPhone() != null) {
                complainParams.put("complainPhone", req.getComplainPhone());
            }

            extend.put("complaintParams", complainParams.toJSONString());

            // 测试参数
            String corpId = JINGXUAN_CORP_ID;
            String botUserId = "majingxian";
            String externalUserId = "wmOrVVCwAAebOGnTreOPX1MmNTUoDjmg";
            String contactName = req.getContactName() != null ? req.getContactName() : "测试用户";

            // 调用延迟创建客诉单检查方法
            Method checkMethod = lbgAiReplyComponent.getClass().getDeclaredMethod(
                    "checkAndScheduleComplaintCreation", Map.class, String.class, String.class, String.class, String.class);
            checkMethod.setAccessible(true);
            checkMethod.invoke(lbgAiReplyComponent, extend, corpId, botUserId, externalUserId, contactName);

            // 构造响应
            TestDelayedComplaintCreationResp response = new TestDelayedComplaintCreationResp();
            response.setOrderId(req.getOrderId());
            response.setTestParams(complainParams.toJSONString());
            response.setCorpId(corpId);
            response.setBotUserId(botUserId);
            response.setExternalUserId(externalUserId);
            response.setContactName(contactName);
            response.setMessage("延迟创建客诉单任务已提交，将在3分钟后检查用户回复并决定是否创建客诉单");

            log.info("测试延迟创建客诉单完成，订单ID：{}，参数：{}", req.getOrderId(), complainParams.toJSONString());
            return Result.success(response);

        } catch (Exception e) {
            log.error("测试延迟创建客诉单失败", e);
            return Result.failure("测试失败：" + e.getMessage());
        }
    }

    // DTO类定义
    @Data
    public static class AnalyzeUserChatReq {
        @NotBlank(message = "用户ID不能为空")
        private String userId;

        @NotBlank(message = "分析日期不能为空")
        private String analysisDate;

    }

    @Data
    public static class AnalyzeUserChatResp {
        private String userId;
        private String analysisDate;
        private int externalUserCount;
        private int processedCount;
        private String message;
    }

    @Data
    public static class DeleteByDateResp {
        private long deletedCount;
        private String operation;
        private String message;
    }

    @Data
    public static class TestCreateComplaintOrderReq {
        private Long orderId;

        private String beComplainIdentity = "商家"; // 被投诉人身份：商家、劳动者

        private String complainDescription; // 投诉描述

        private String complainReason; // 投诉原因

        private Integer complainType; // 投诉类型

        private Boolean isChangePhone = false; // 是否更换电话

        private String complainPhone; // 投诉人电话（当isChangePhone为true时使用）

        private String contactName; // 联系人姓名
    }

    @Data
    public static class TestCreateComplaintOrderResp {
        private Long orderId;
        private String testParams;
        private String corpId;
        private String botUserId;
        private String externalUserId;
        private String contactName;
        private String message;
    }

}
