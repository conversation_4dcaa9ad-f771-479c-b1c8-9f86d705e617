# 延迟创建客诉单功能说明

## 功能概述

当AI客服返回type=0（普通消息）且extend中包含`isPushComplaint=true`和`complaintParams`时，系统会在发送消息给用户后启动一个3分钟的延迟任务。如果用户在3分钟内没有回复，系统将自动创建客诉单。

## 实现逻辑

### 1. 触发条件
- AI返回结果的type=0（普通消息）
- extend中包含`isPushComplaint=true`
- extend中包含`complaintParams`（JSON格式的客诉单参数）

### 2. 处理流程
1. AI发送普通消息给用户
2. 检查extend中的isPushComplaint标志
3. 如果满足条件，启动3分钟延迟任务
4. 3分钟后检查用户是否有回复
5. 如果没有回复，自动创建客诉单

### 3. 核心组件

#### LbgAiReplyComponent
- `handleAsyncNormalMessage()`: 处理type=0消息时调用延迟检查
- `checkAndScheduleComplaintCreation()`: 检查并安排客诉单创建
- `scheduleDelayedComplaintCreation()`: 安排延迟创建客诉单任务
- `tryCreateComplaintOrder()`: 创建客诉单（已改为public方法）

#### LbgAiAsyncReplyHandler
- `commitDelayedComplaintCreationTask()`: 提交延迟任务
- `handleDelayedComplaintCreation()`: 处理延迟创建客诉单
- `checkUserReplyInLastMinutes()`: 检查用户是否在指定时间内回复（从Redis聊天记录中检查）

## 参数说明

### extend参数格式
```json
{
  "isPushComplaint": "true",
  "complaintParams": "{\"orderId\":12345,\"beComplainIdentity\":\"商家\",\"complainDescription\":\"服务质量问题\",\"isChangePhone\":false,\"complainPhone\":\"13800138000\"}"
}
```

### complaintParams参数说明
- `orderId`: 订单ID（必填）
- `beComplainIdentity`: 被投诉人身份（商家/劳动者）
- `complainDescription`: 投诉描述
- `isChangePhone`: 是否更换联系电话
- `complainPhone`: 投诉人联系电话
- `complainType`: 投诉类型
- `complainReason`: 投诉原因

## 测试接口

### 接口路径
`POST /test/dify-chat-analysis/test-delayed-complaint-creation`

### 请求参数
```json
{
  "orderId": 12345,
  "beComplainIdentity": "商家",
  "complainDescription": "测试延迟创建客诉单",
  "isChangePhone": false,
  "complainPhone": "13800138000",
  "contactName": "测试用户"
}
```

### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "orderId": 12345,
    "testParams": "{\"orderId\":12345,\"beComplainIdentity\":\"商家\",\"complainDescription\":\"测试延迟创建客诉单\",\"isChangePhone\":false}",
    "corpId": "ww5cfa32107e9a1f20",
    "botUserId": "majingxian",
    "externalUserId": "wmOrVVCwAAebOGnTreOPX1MmNTUoDjmg",
    "contactName": "测试用户",
    "message": "延迟创建客诉单任务已提交，将在3分钟后检查用户回复并决定是否创建客诉单"
  }
}
```

## 日志关键字

在日志中搜索以下关键字可以查看功能执行详情：

- `检测到需要延迟创建客诉单`
- `开始处理延迟创建客诉单任务`
- `检测到用户在3分钟内有回复`
- `用户在3分钟内无回复，开始创建客诉单`
- `处理延迟创建客诉单异常`

## 注意事项

1. **环境限制**: 测试接口仅在非生产环境可用
2. **时间检查**: 系统会检查3分钟内是否有用户发送的消息（从Redis聊天记录中检查isUserSend=true的消息）
3. **异常处理**: 如果检查用户回复时发生异常，为安全起见会认为用户有回复，不创建客诉单
4. **数据源**: 直接从Redis中的聊天记录检查，不查询数据库
5. **延迟任务**: 使用WMB延迟队列实现，生产环境3分钟延迟，沙箱环境直接执行

## 相关代码文件

- `LbgAiReplyComponent.java`: 主要业务逻辑
- `LbgAiAsyncReplyHandler.java`: 异步任务处理
- `DifyChatAnalysisTestController.java`: 测试接口
- `JuziSingleChatContentRecordJpaRepository.java`: 聊天记录查询
