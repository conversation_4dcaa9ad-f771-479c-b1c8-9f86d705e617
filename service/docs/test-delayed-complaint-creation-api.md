# 测试延迟创建客诉单API使用说明

## 接口信息

**接口路径**: `/test/dify-chat-analysis/test-delayed-complaint-creation`  
**请求方法**: POST  
**Content-Type**: application/json

## 功能说明

该接口用于测试延迟创建客诉单功能，模拟当AI客服返回type=0且extend中包含`isPushComplaint=true`时的完整流程。

## 请求参数

```json
{
    "orderId": 12345,
    "beComplainIdentity": "商家",
    "complainDescription": "测试延迟创建客诉单功能",
    "isChangePhone": false,
    "complainPhone": "13800138000",
    "contactName": "测试用户"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |
| beComplainIdentity | String | 否 | 被投诉人身份（商家/劳动者），默认为"商家" |
| complainDescription | String | 否 | 投诉描述，默认为"测试延迟创建客诉单" |
| isChangePhone | Boolean | 否 | 是否更换联系电话，默认为false |
| complainPhone | String | 否 | 投诉人联系电话 |
| contactName | String | 否 | 联系人姓名，默认为"测试用户" |

## 响应示例

### 成功响应
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "orderId": 12345,
        "testParams": "{\"orderId\":12345,\"beComplainIdentity\":\"商家\",\"complainDescription\":\"测试延迟创建客诉单功能\",\"isChangePhone\":false}",
        "corpId": "ww5cfa32107e9a1f20",
        "botUserId": "majingxian",
        "externalUserId": "wmOrVVCwAAebOGnTreOPX1MmNTUoDjmg",
        "contactName": "测试用户",
        "message": "延迟创建客诉单任务已提交，将在3分钟后检查用户回复并决定是否创建客诉单"
    }
}
```

### 错误响应
```json
{
    "code": -1,
    "msg": "测试失败：具体错误信息",
    "data": null
}
```

## 测试流程

1. **调用测试接口**: 发送POST请求到测试接口
2. **任务提交**: 系统会提交一个3分钟延迟的WMB任务
3. **等待执行**: 3分钟后任务会被执行
4. **检查用户回复**: 系统会检查3分钟内是否有用户回复
5. **创建客诉单**: 如果没有用户回复，则自动创建客诉单

## 测试场景

### 场景1：用户无回复，创建客诉单
1. 调用测试接口
2. 等待3分钟，不发送任何用户消息
3. 系统应该自动创建客诉单

### 场景2：用户有回复，不创建客诉单
1. 调用测试接口
2. 在3分钟内发送用户消息（sendBy=1）
3. 系统应该检测到用户回复，不创建客诉单

## 验证方法

### 1. 查看日志
搜索以下关键字来跟踪执行过程：
- `检测到需要延迟创建客诉单`
- `开始处理延迟创建客诉单任务`
- `检测到用户在3分钟内有回复`
- `用户在3分钟内无回复，开始创建客诉单`

### 2. 检查数据库
- 查看客诉单表是否有新记录
- 检查聊天记录表中的用户消息

### 3. 监控WMB任务
- 确认延迟任务是否正确提交
- 验证任务是否在3分钟后执行

## 注意事项

1. **环境限制**: 该接口仅在非生产环境可用，生产环境会返回错误
2. **测试数据**: 使用固定的测试企业ID和用户ID
3. **时间控制**: 沙箱环境可能直接执行，生产环境使用真实的3分钟延迟
4. **并发测试**: 避免同时进行多个测试，可能会相互影响
5. **数据清理**: 测试完成后注意清理测试数据

## 相关接口

- 创建客诉单测试: `/test/dify-chat-analysis/test-create-complaint-order`
- 聊天分析测试: `/test/dify-chat-analysis/analyze-user-chat`

## 故障排查

### 常见问题

1. **任务未执行**: 检查WMB配置和延迟队列状态
2. **用户回复检测失败**: 确认聊天记录查询逻辑
3. **客诉单创建失败**: 检查订单信息和客诉服务状态
4. **权限问题**: 确认在非生产环境执行

### 日志级别
建议将相关类的日志级别设置为DEBUG以获取详细信息：
- `com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiReplyComponent`
- `com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.LbgAiAsyncReplyHandler`
